# 股票K线数据获取和存储系统

这是一个基于Python的股票K线数据获取和存储系统，可以从雪球网获取股票数据并保存到HDF5格式文件中。

## 功能特性

1. **批量获取股票数据**: 根据股票列表批量获取800根日线数据
2. **增量数据更新**: 定期更新股票数据，只获取新增的数据
3. **HDF5存储**: 使用高效的HDF5格式存储数据，支持压缩
4. **错误处理**: 完善的错误处理和重试机制
5. **进度显示**: 实时显示数据获取进度

## 技术栈

- Python 3.x
- pandas: 数据处理
- h5py/tables: HDF5文件操作
- requests: HTTP请求

## 安装和使用

### 1. 环境准备

```bash
# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置股票列表

编辑 `stock_list_cache.json` 文件，添加你要获取的股票代码：

```json
{
  "timestamp": "2025-08-13T10:00:00.000000",
  "data": [
    {
      "code": "000001",
      "name": "平安银行"
    },
    {
      "code": "600519",
      "name": "贵州茅台"
    }
  ]
}
```

### 3. 批量获取数据

```bash
# 激活虚拟环境
source venv/bin/activate

# 运行批量获取脚本
python stock_data_fetcher.py
```

### 4. 增量更新数据

```bash
# 激活虚拟环境
source venv/bin/activate

# 运行增量更新脚本
python update_stock_data.py
```

## 数据格式

获取的K线数据包含以下字段：

- `timestamp`: 时间戳
- `volume`: 成交量
- `open`: 开盘价
- `high`: 最高价
- `low`: 最低价
- `close`: 收盘价
- `chg`: 涨跌额
- `percent`: 涨跌幅
- `turnoverrate`: 换手率
- `amount`: 成交额
- `volume_post`: 盘后成交量
- `amount_post`: 盘后成交额
- `code`: 股票代码

## 数据查看

```python
import pandas as pd

# 查看所有股票
with pd.HDFStore('stock_data.h5', 'r') as store:
    print(store.keys())

# 查看特定股票数据
with pd.HDFStore('stock_data.h5', 'r') as store:
    df = store.get('/stock_000001')  # 平安银行
    print(df.head())
    print(f"数据条数: {len(df)}")
    print(f"日期范围: {df['timestamp'].min()} 到 {df['timestamp'].max()}")
```

## 定时任务

可以使用cron设置定时任务来自动更新数据：

```bash
# 编辑crontab
crontab -e

# 添加定时任务（每天18:00更新）
0 18 * * * cd /path/to/stock_data && source venv/bin/activate && python update_stock_data.py
```

## 注意事项

1. **Cookie配置**: 需要在 `stock_data_fetcher.py` 中配置有效的雪球Cookie
2. **请求频率**: 程序已设置请求间隔，避免过于频繁的请求
3. **数据准确性**: 数据来源于雪球网，仅供参考，不构成投资建议
4. **存储空间**: HDF5文件会随着数据增加而增大，注意磁盘空间

## 文件说明

- `stock_data_fetcher.py`: 主程序文件，包含所有核心功能
- `update_stock_data.py`: 增量更新脚本
- `stock_list_cache.json`: 股票列表配置文件
- `stock_data.h5`: HDF5数据文件
- `requirements.txt`: Python依赖包列表
- `README.md`: 使用说明文档

## 许可证

本项目仅供学习和研究使用。
