#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据增量更新脚本
"""

from stock_data_fetcher import update_stock_data_incremental

if __name__ == "__main__":
    print("=" * 60)
    print("股票K线数据增量更新")
    print("=" * 60)
    print("\n开始增量更新股票数据...")
    
    results = update_stock_data_incremental()
    
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    print(f"\n增量更新完成: 成功 {success_count}/{total_count} 只股票")
    
    if success_count > 0:
        print(f"\n数据已更新到 stock_data.h5 文件中")
