#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票K线数据获取和存储系统
"""

import json
import time
import requests
import pandas as pd
import h5py
import numpy as np
from datetime import datetime, timedelta
import os
import logging
from typing import List, Dict, Optional, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 雪球Cookie - 需要用户提供有效的Cookie
XQ_COOKIE = 'cookiesu=511750991532166; device_id=cd7a4d660774a60c4265c292c3283d3c; s=c11ac7hq0t; xq_is_login=1; u=7166875318; acw_tc=ac11000117549686263807702ef3e80f75cce7a1992f33627ab2366bf9b374; xq_a_token=04cd6b08ad9be8f12b2202aa8336ddec59e01c70; xqat=04cd6b08ad9be8f12b2202aa8336ddec59e01c70; xq_id_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; xq_r_token=6f238659f3d34d9c74a5790d56fa413b55daf493; is_overseas=0; .thumbcache_f24b8bbe5a5934237bbc0eda20c1b6e7=jumZnERBJLKXfXBZkuJCMtiBiPbGyV0kQrA++09CNQLJU8mh8YB48kcNzZOKffoBpMC/8twVfI4q5qm0GgQc+w%3D%3D; ssxmod_itna=1-iq_x9DcQ0QqCTxeKxmwd4QkP1D73DzxC5iOD_xQ5DODLxn4GQDUjYTng4QI3Yhw4PqD02DQ5dY48DjzDBwD4iDnqD8GC=i7vzKlGD3hvKEiDeQeKSlC0Dc0YDkh77q8zUEknfo09=CgeniWQxiTrmqDoxGkDi5oD0IbGDiiDBeD5xDTDWeDGDD3dSBw1U7tRDDdp9Yms4GWDmbEDY5DEhRvDiPhR4DWN4i1AngpuKxD0ra6gQQDN1Qo3fOvkfYDvaL_mASurNZD9DkTH40kmkZwmjSEG33mLdqjIa51GjPQ4x44N6Dx4rrkFGxKGb/iC8ubQiNAD4eAqGr5AiNj5PybXQuF3w4dG4dygb5l0dX2eZxiK_xQoPDA/RgDsY47RZAGDD; ssxmod_itna2=1-iq_x9DcQ0QqCTxeKxmwd4QkP1D73DzxC5iOD_xQ5DODLxn4GQDUjYTng4QI3Yhw4PqD02DQ5dY48DQiDGfvpzdK47PhdAF2xD/Qicpif0SG7dhXqAUGLdeHESYdxwk2Kw347I6_5rOh5rcDUoF546aQ0lPFWn2zUBPQqCYmshiFTUa5sEYbKjyIdjDAf=88yofOvg4xhp3QL_0O=79ENWSF2PqPBUEdE6iOUixPvzw81Dq4G7Gx1BiNkSrCT1OuNqb5YPjIqfo1eiDbIxLf10C9ETiNVQiieo32qm/gLsz54D'

def format_stock_code(code: str) -> str:
    """
    格式化股票代码为雪球API需要的格式
    """
    if code.startswith('6'):
        return f'SH{code}'
    elif code.startswith('0') or code.startswith('3'):
        return f'SZ{code}'
    else:
        return code.upper()

def get_kline_data(code: str, count: int = 800, period: str = 'day', 
                   xq_cookie: str = XQ_COOKIE, max_retries: int = 3) -> Optional[pd.DataFrame]:
    """
    获取股票K线数据
    
    Args:
        code: 股票代码 (如 '000001')
        count: 获取K线数量 (默认800)
        period: 周期 ('day', 'week')
        xq_cookie: 雪球Cookie
        max_retries: 最大重试次数
    
    Returns:
        DataFrame: K线数据，包含列 [timestamp, volume, open, high, low, close, chg, percent, turnoverrate, amount, volume_post, amount_post]
        None: 获取失败时返回None
    """
    formatted_code = format_stock_code(code)
    current_timestamp = int(time.time() * 1000)
    
    url = f"https://stock.xueqiu.com/v5/stock/chart/kline.json?symbol={formatted_code}&begin={current_timestamp}&period={period}&type=before&count=-{count}&indicator=kline"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Safari/537.36',
        'Cookie': xq_cookie
    }
    
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('error_code') != 0:
                logger.error(f"API错误 {code}: {data.get('error_description', 'Unknown error')}")
                return None
            
            stock_data = data.get('data', {})
            columns = stock_data.get('column', [])
            items = stock_data.get('item', [])
            
            if not columns or not items:
                logger.warning(f"股票 {code} 没有数据")
                return None
            
            # 创建DataFrame
            df = pd.DataFrame(items, columns=columns)
            
            # 转换时间戳为日期
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

            # 添加股票代码列
            df['code'] = code
            
            # 按日期排序（从旧到新）
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            logger.info(f"成功获取股票 {code} 的 {len(df)} 条K线数据")
            return df
            
        except requests.exceptions.RequestException as e:
            logger.warning(f"获取股票 {code} 数据失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
        except Exception as e:
            logger.error(f"处理股票 {code} 数据时出错: {e}")
            break
    
    logger.error(f"获取股票 {code} 数据最终失败")
    return None

def load_stock_list(file_path: str = 'stock_list_cache.json') -> List[Dict[str, str]]:
    """
    加载股票列表

    Args:
        file_path: 股票列表文件路径

    Returns:
        List[Dict]: 股票列表，每个元素包含 'code' 和 'name'
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        stock_list = data.get('data', [])
        logger.info(f"加载了 {len(stock_list)} 只股票")
        return stock_list

    except FileNotFoundError:
        logger.error(f"股票列表文件 {file_path} 不存在")
        return []
    except Exception as e:
        logger.error(f"加载股票列表失败: {e}")
        return []

def save_to_hdf5(df: pd.DataFrame, code: str, hdf5_file: str = 'stock_data.h5') -> bool:
    """
    将K线数据保存到HDF5文件

    Args:
        df: K线数据DataFrame
        code: 股票代码
        hdf5_file: HDF5文件路径

    Returns:
        bool: 保存是否成功
    """
    try:
        # 确保数据类型正确
        df_copy = df.copy()

        # 转换数据类型以优化存储
        numeric_columns = ['volume', 'open', 'high', 'low', 'close', 'chg', 'percent',
                          'turnoverrate', 'amount', 'volume_post', 'amount_post']

        for col in numeric_columns:
            if col in df_copy.columns:
                df_copy[col] = pd.to_numeric(df_copy[col], errors='coerce')

        # 使用HDFStore保存数据
        with pd.HDFStore(hdf5_file, mode='a', complevel=9, complib='zlib') as store:
            key = f'/stock_{code}'
            store.put(key, df_copy, format='table', data_columns=True)

        logger.info(f"股票 {code} 数据已保存到 {hdf5_file}")
        return True

    except Exception as e:
        logger.error(f"保存股票 {code} 数据到HDF5失败: {e}")
        return False

def load_from_hdf5(code: str, hdf5_file: str = 'stock_data.h5') -> Optional[pd.DataFrame]:
    """
    从HDF5文件加载K线数据

    Args:
        code: 股票代码
        hdf5_file: HDF5文件路径

    Returns:
        DataFrame: K线数据，如果不存在返回None
    """
    try:
        key = f'/stock_{code}'
        with pd.HDFStore(hdf5_file, mode='r') as store:
            if key in store:
                df = store.get(key)
                logger.info(f"从HDF5加载股票 {code} 的 {len(df)} 条数据")
                return df
            else:
                logger.info(f"HDF5中不存在股票 {code} 的数据")
                return None

    except FileNotFoundError:
        logger.info(f"HDF5文件 {hdf5_file} 不存在")
        return None
    except Exception as e:
        logger.error(f"从HDF5加载股票 {code} 数据失败: {e}")
        return None

def get_latest_date_from_hdf5(code: str, hdf5_file: str = 'stock_data.h5') -> Optional[datetime]:
    """
    获取HDF5中某只股票的最新日期

    Args:
        code: 股票代码
        hdf5_file: HDF5文件路径

    Returns:
        datetime: 最新日期，如果不存在返回None
    """
    df = load_from_hdf5(code, hdf5_file)
    if df is not None and not df.empty:
        return df['timestamp'].max()
    return None

def fetch_all_stocks_data(stock_list_file: str = 'stock_list_cache.json',
                         hdf5_file: str = 'stock_data.h5',
                         count: int = 800,
                         delay: float = 1.0) -> Dict[str, bool]:
    """
    批量获取所有股票的K线数据并保存到HDF5

    Args:
        stock_list_file: 股票列表文件路径
        hdf5_file: HDF5文件路径
        count: 获取K线数量
        delay: 请求间隔时间（秒）

    Returns:
        Dict[str, bool]: 每只股票的处理结果
    """
    stock_list = load_stock_list(stock_list_file)
    if not stock_list:
        logger.error("没有加载到股票列表")
        return {}

    results = {}
    total_stocks = len(stock_list)

    logger.info(f"开始获取 {total_stocks} 只股票的 {count} 根K线数据")

    for i, stock in enumerate(stock_list, 1):
        code = stock['code']
        name = stock['name']

        logger.info(f"[{i}/{total_stocks}] 正在处理: {code} - {name}")

        try:
            # 获取K线数据
            df = get_kline_data(code, count=count)

            if df is not None and not df.empty:
                # 保存到HDF5
                success = save_to_hdf5(df, code, hdf5_file)
                results[code] = success

                if success:
                    logger.info(f"✓ {code} - {name}: 成功获取并保存 {len(df)} 条数据")
                else:
                    logger.error(f"✗ {code} - {name}: 保存失败")
            else:
                logger.error(f"✗ {code} - {name}: 获取数据失败")
                results[code] = False

        except Exception as e:
            logger.error(f"✗ {code} - {name}: 处理异常 - {e}")
            results[code] = False

        # 请求间隔
        if i < total_stocks:
            time.sleep(delay)

    # 统计结果
    success_count = sum(1 for success in results.values() if success)
    fail_count = total_stocks - success_count

    logger.info(f"批量获取完成: 成功 {success_count} 只，失败 {fail_count} 只")

    return results

def calculate_days_needed(latest_date: datetime) -> int:
    """
    计算需要获取的天数

    Args:
        latest_date: 最新数据日期

    Returns:
        int: 需要获取的天数
    """
    today = datetime.now()
    days_diff = (today - latest_date).days

    # 考虑到周末和节假日，多获取一些数据以确保覆盖
    return min(max(days_diff + 10, 30), 200)  # 最少30天，最多200天

def update_stock_data_incremental(stock_list_file: str = 'stock_list_cache.json',
                                 hdf5_file: str = 'stock_data.h5',
                                 delay: float = 1.0) -> Dict[str, bool]:
    """
    增量更新股票K线数据

    Args:
        stock_list_file: 股票列表文件路径
        hdf5_file: HDF5文件路径
        delay: 请求间隔时间（秒）

    Returns:
        Dict[str, bool]: 每只股票的更新结果
    """
    stock_list = load_stock_list(stock_list_file)
    if not stock_list:
        logger.error("没有加载到股票列表")
        return {}

    results = {}
    total_stocks = len(stock_list)
    updated_count = 0

    logger.info(f"开始增量更新 {total_stocks} 只股票的K线数据")

    for i, stock in enumerate(stock_list, 1):
        code = stock['code']
        name = stock['name']

        logger.info(f"[{i}/{total_stocks}] 检查更新: {code} - {name}")

        try:
            # 获取现有数据的最新日期
            latest_date = get_latest_date_from_hdf5(code, hdf5_file)

            if latest_date is None:
                logger.info(f"{code} - {name}: 没有历史数据，跳过增量更新")
                results[code] = False
                continue

            # 计算需要获取的天数
            days_needed = calculate_days_needed(latest_date)

            # 如果最新数据很新（小于3天），跳过更新
            if days_needed < 5:
                logger.info(f"{code} - {name}: 数据已是最新，跳过更新")
                results[code] = True
                continue

            logger.info(f"{code} - {name}: 最新数据日期 {latest_date.strftime('%Y-%m-%d')}，需要获取 {days_needed} 天数据")

            # 获取新的K线数据
            new_df = get_kline_data(code, count=days_needed)

            if new_df is not None and not new_df.empty:
                # 加载现有数据
                existing_df = load_from_hdf5(code, hdf5_file)

                if existing_df is not None:
                    # 合并数据，去重
                    combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                    combined_df = combined_df.drop_duplicates(subset=['timestamp'], keep='last')
                    combined_df = combined_df.sort_values('timestamp').reset_index(drop=True)

                    # 检查是否有新数据
                    if len(combined_df) > len(existing_df):
                        # 保存更新后的数据
                        success = save_to_hdf5(combined_df, code, hdf5_file)
                        results[code] = success

                        if success:
                            new_records = len(combined_df) - len(existing_df)
                            logger.info(f"✓ {code} - {name}: 成功更新 {new_records} 条新数据")
                            updated_count += 1
                        else:
                            logger.error(f"✗ {code} - {name}: 保存更新数据失败")
                    else:
                        logger.info(f"{code} - {name}: 没有新数据需要更新")
                        results[code] = True
                else:
                    logger.error(f"✗ {code} - {name}: 无法加载现有数据")
                    results[code] = False
            else:
                logger.error(f"✗ {code} - {name}: 获取新数据失败")
                results[code] = False

        except Exception as e:
            logger.error(f"✗ {code} - {name}: 更新异常 - {e}")
            results[code] = False

        # 请求间隔
        if i < total_stocks:
            time.sleep(delay)

    # 统计结果
    success_count = sum(1 for success in results.values() if success)
    fail_count = total_stocks - success_count

    logger.info(f"增量更新完成: 成功 {success_count} 只，失败 {fail_count} 只，实际更新 {updated_count} 只")

    return results

def main():
    """
    主函数 - 提供用户交互界面
    """
    print("=" * 60)
    print("股票K线数据获取和存储系统")
    print("=" * 60)

    while True:
        print("\n请选择操作:")
        print("1. 批量获取所有股票800根K线数据")
        print("2. 增量更新股票数据")
        print("3. 查看HDF5文件中的股票列表")
        print("4. 查看指定股票的数据统计")
        print("5. 退出")

        choice = input("\n请输入选择 (1-5): ").strip()

        if choice == '1':
            print("\n开始批量获取股票数据...")
            results = fetch_all_stocks_data()

            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            print(f"\n批量获取完成: 成功 {success_count}/{total_count} 只股票")

        elif choice == '2':
            print("\n开始增量更新股票数据...")
            results = update_stock_data_incremental()

            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            print(f"\n增量更新完成: 成功 {success_count}/{total_count} 只股票")

        elif choice == '3':
            try:
                with pd.HDFStore('stock_data.h5', mode='r') as store:
                    keys = store.keys()
                    if keys:
                        print(f"\nHDF5文件中包含 {len(keys)} 只股票的数据:")
                        for key in sorted(keys):
                            stock_code = key.replace('/stock_', '')
                            df = store.get(key)
                            print(f"  {stock_code}: {len(df)} 条记录")
                    else:
                        print("\nHDF5文件中没有数据")
            except FileNotFoundError:
                print("\nHDF5文件不存在")
            except Exception as e:
                print(f"\n读取HDF5文件失败: {e}")

        elif choice == '4':
            code = input("\n请输入股票代码 (如 000001): ").strip()
            if code:
                df = load_from_hdf5(code)
                if df is not None:
                    print(f"\n股票 {code} 数据统计:")
                    print(f"  总记录数: {len(df)}")
                    print(f"  日期范围: {df['timestamp'].min().strftime('%Y-%m-%d')} 到 {df['timestamp'].max().strftime('%Y-%m-%d')}")
                    print(f"  最新收盘价: {df['close'].iloc[-1]:.2f}")
                    print(f"  最高价: {df['high'].max():.2f}")
                    print(f"  最低价: {df['low'].min():.2f}")
                else:
                    print(f"\n未找到股票 {code} 的数据")

        elif choice == '5':
            print("\n感谢使用！")
            break

        else:
            print("\n无效选择，请重新输入")

if __name__ == "__main__":
    # 直接执行批量获取功能
    print("=" * 60)
    print("股票K线数据获取和存储系统")
    print("=" * 60)
    print("\n开始批量获取所有股票800根K线数据...")

    results = fetch_all_stocks_data()

    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    print(f"\n批量获取完成: 成功 {success_count}/{total_count} 只股票")

    if success_count > 0:
        print(f"\n数据已保存到 stock_data.h5 文件中")
        print("可以使用以下代码查看数据:")
        print("import pandas as pd")
        print("with pd.HDFStore('stock_data.h5', 'r') as store:")
        print("    print(store.keys())")
        print("    df = store.get('/stock_000001')  # 查看平安银行数据")
        print("    print(df.head())")
