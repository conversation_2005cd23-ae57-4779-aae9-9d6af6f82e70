#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票K线数据获取和存储系统
"""

import json
import time
import requests
import pandas as pd
import h5py
import numpy as np
from datetime import datetime, timedelta
import os
import logging
from typing import List, Dict, Optional, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 雪球Cookie - 需要用户提供有效的Cookie
XQ_COOKIE = 'cookiesu=511750991532166; device_id=cd7a4d660774a60c4265c292c3283d3c; s=c11ac7hq0t; xq_is_login=1; u=7166875318; acw_tc=ac11000117549686263807702ef3e80f75cce7a1992f33627ab2366bf9b374; xq_a_token=04cd6b08ad9be8f12b2202aa8336ddec59e01c70; xqat=04cd6b08ad9be8f12b2202aa8336ddec59e01c70; xq_id_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; xq_r_token=6f238659f3d34d9c74a5790d56fa413b55daf493; is_overseas=0; .thumbcache_f24b8bbe5a5934237bbc0eda20c1b6e7=jumZnERBJLKXfXBZkuJCMtiBiPbGyV0kQrA++09CNQLJU8mh8YB48kcNzZOKffoBpMC/8twVfI4q5qm0GgQc+w%3D%3D; ssxmod_itna=1-iq_x9DcQ0QqCTxeKxmwd4QkP1D73DzxC5iOD_xQ5DODLxn4GQDUjYTng4QI3Yhw4PqD02DQ5dY48DjzDBwD4iDnqD8GC=i7vzKlGD3hvKEiDeQeKSlC0Dc0YDkh77q8zUEknfo09=CgeniWQxiTrmqDoxGkDi5oD0IbGDiiDBeD5xDTDWeDGDD3dSBw1U7tRDDdp9Yms4GWDmbEDY5DEhRvDiPhR4DWN4i1AngpuKxD0ra6gQQDN1Qo3fOvkfYDvaL_mASurNZD9DkTH40kmkZwmjSEG33mLdqjIa51GjPQ4x44N6Dx4rrkFGxKGb/iC8ubQiNAD4eAqGr5AiNj5PybXQuF3w4dG4dygb5l0dX2eZxiK_xQoPDA/RgDsY47RZAGDD; ssxmod_itna2=1-iq_x9DcQ0QqCTxeKxmwd4QkP1D73DzxC5iOD_xQ5DODLxn4GQDUjYTng4QI3Yhw4PqD02DQ5dY48DQiDGfvpzdK47PhdAF2xD/Qicpif0SG7dhXqAUGLdeHESYdxwk2Kw347I6_5rOh5rcDUoF546aQ0lPFWn2zUBPQqCYmshiFTUa5sEYbKjyIdjDAf=88yofOvg4xhp3QL_0O=79ENWSF2PqPBUEdE6iOUixPvzw81Dq4G7Gx1BiNkSrCT1OuNqb5YPjIqfo1eiDbIxLf10C9ETiNVQiieo32qm/gLsz54D'

def format_stock_code(code: str) -> str:
    """
    格式化股票代码为雪球API需要的格式
    """
    if code.startswith('6'):
        return f'SH{code}'
    elif code.startswith('0') or code.startswith('3'):
        return f'SZ{code}'
    else:
        return code.upper()

def get_kline_data(code: str, count: int = 800, period: str = 'day', 
                   xq_cookie: str = XQ_COOKIE, max_retries: int = 3) -> Optional[pd.DataFrame]:
    """
    获取股票K线数据
    
    Args:
        code: 股票代码 (如 '000001')
        count: 获取K线数量 (默认800)
        period: 周期 ('day', 'week')
        xq_cookie: 雪球Cookie
        max_retries: 最大重试次数
    
    Returns:
        DataFrame: K线数据，包含列 [timestamp, volume, open, high, low, close, chg, percent, turnoverrate, amount, volume_post, amount_post]
        None: 获取失败时返回None
    """
    formatted_code = format_stock_code(code)
    current_timestamp = int(time.time() * 1000)
    
    url = f"https://stock.xueqiu.com/v5/stock/chart/kline.json?symbol={formatted_code}&begin={current_timestamp}&period={period}&type=before&count=-{count}&indicator=kline"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Safari/537.36',
        'Cookie': xq_cookie
    }
    
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('error_code') != 0:
                logger.error(f"API错误 {code}: {data.get('error_description', 'Unknown error')}")
                return None
            
            stock_data = data.get('data', {})
            columns = stock_data.get('column', [])
            items = stock_data.get('item', [])
            
            if not columns or not items:
                logger.warning(f"股票 {code} 没有数据")
                return None
            
            # 创建DataFrame
            df = pd.DataFrame(items, columns=columns)
            
            # 转换时间戳为日期
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['date'] = df['timestamp'].dt.date
            
            # 添加股票代码列
            df['code'] = code
            
            # 按日期排序（从旧到新）
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            logger.info(f"成功获取股票 {code} 的 {len(df)} 条K线数据")
            return df
            
        except requests.exceptions.RequestException as e:
            logger.warning(f"获取股票 {code} 数据失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
        except Exception as e:
            logger.error(f"处理股票 {code} 数据时出错: {e}")
            break
    
    logger.error(f"获取股票 {code} 数据最终失败")
    return None

def load_stock_list(file_path: str = 'stock_list_cache.json') -> List[Dict[str, str]]:
    """
    加载股票列表

    Args:
        file_path: 股票列表文件路径

    Returns:
        List[Dict]: 股票列表，每个元素包含 'code' 和 'name'
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        stock_list = data.get('data', [])
        logger.info(f"加载了 {len(stock_list)} 只股票")
        return stock_list

    except FileNotFoundError:
        logger.error(f"股票列表文件 {file_path} 不存在")
        return []
    except Exception as e:
        logger.error(f"加载股票列表失败: {e}")
        return []

def save_to_hdf5(df: pd.DataFrame, code: str, hdf5_file: str = 'stock_data.h5') -> bool:
    """
    将K线数据保存到HDF5文件

    Args:
        df: K线数据DataFrame
        code: 股票代码
        hdf5_file: HDF5文件路径

    Returns:
        bool: 保存是否成功
    """
    try:
        # 确保数据类型正确
        df_copy = df.copy()

        # 转换数据类型以优化存储
        numeric_columns = ['volume', 'open', 'high', 'low', 'close', 'chg', 'percent',
                          'turnoverrate', 'amount', 'volume_post', 'amount_post']

        for col in numeric_columns:
            if col in df_copy.columns:
                df_copy[col] = pd.to_numeric(df_copy[col], errors='coerce')

        # 使用HDFStore保存数据
        with pd.HDFStore(hdf5_file, mode='a', complevel=9, complib='zlib') as store:
            key = f'/stock_{code}'
            store.put(key, df_copy, format='table', data_columns=True)

        logger.info(f"股票 {code} 数据已保存到 {hdf5_file}")
        return True

    except Exception as e:
        logger.error(f"保存股票 {code} 数据到HDF5失败: {e}")
        return False

def load_from_hdf5(code: str, hdf5_file: str = 'stock_data.h5') -> Optional[pd.DataFrame]:
    """
    从HDF5文件加载K线数据

    Args:
        code: 股票代码
        hdf5_file: HDF5文件路径

    Returns:
        DataFrame: K线数据，如果不存在返回None
    """
    try:
        key = f'/stock_{code}'
        with pd.HDFStore(hdf5_file, mode='r') as store:
            if key in store:
                df = store.get(key)
                logger.info(f"从HDF5加载股票 {code} 的 {len(df)} 条数据")
                return df
            else:
                logger.info(f"HDF5中不存在股票 {code} 的数据")
                return None

    except FileNotFoundError:
        logger.info(f"HDF5文件 {hdf5_file} 不存在")
        return None
    except Exception as e:
        logger.error(f"从HDF5加载股票 {code} 数据失败: {e}")
        return None

def get_latest_date_from_hdf5(code: str, hdf5_file: str = 'stock_data.h5') -> Optional[datetime]:
    """
    获取HDF5中某只股票的最新日期

    Args:
        code: 股票代码
        hdf5_file: HDF5文件路径

    Returns:
        datetime: 最新日期，如果不存在返回None
    """
    df = load_from_hdf5(code, hdf5_file)
    if df is not None and not df.empty:
        return df['timestamp'].max()
    return None
