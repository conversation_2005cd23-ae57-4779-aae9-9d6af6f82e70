任务：
1. 根据我提供的股票列表文件 stock_list_cache.json 和获取 k 线函数，循环获取列表中的所有股票代码的 800 根日线数据，将 k 线数据用 HDF5 保存到当前目录。方便后面使用；
2. 再写一个增量补充新增数据的函数，用户补全新增的 k 线数据，我可以每天定时调用或者隔几天再手动调用，都要把中间差的数据补全。

## 技术栈
python, pandas, hdf5
虚拟环境目录 venv

## 获取全市场股票代码

stock_list_cache.json 内容格式（仅用于获取股票代码）：

```
 {  
  "timestamp": "2025-07-02T14:52:05.505058",  
  "data": [  
    {  
      "code": "000001",  
      "name": "平安银行"  
    },
    ...
    ]
}
```

## 获取K线详情（用于获取K线详情）

参考代码：
```
XQ_COOKIE = 'cookiesu=511750991532166; device_id=cd7a4d660774a60c4265c292c3283d3c; s=c11ac7hq0t; xq_is_login=1; u=7166875318; acw_tc=ac11000117549686263807702ef3e80f75cce7a1992f33627ab2366bf9b374; xq_a_token=04cd6b08ad9be8f12b2202aa8336ddec59e01c70; xqat=04cd6b08ad9be8f12b2202aa8336ddec59e01c70; xq_id_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; xq_r_token=6f238659f3d34d9c74a5790d56fa413b55daf493; is_overseas=0; .thumbcache_f24b8bbe5a5934237bbc0eda20c1b6e7=jumZnERBJLKXfXBZkuJCMtiBiPbGyV0kQrA++09CNQLJU8mh8YB48kcNzZOKffoBpMC/8twVfI4q5qm0GgQc+w%3D%3D; ssxmod_itna=1-iq_x9DcQ0QqCTxeKxmwd4QkP1D73DzxC5iOD_xQ5DODLxn4GQDUjYTng4QI3Yhw4PqD02DQ5dY48DjzDBwD4iDnqD8GC=i7vzKlGD3hvKEiDeQeKSlC0Dc0YDkh77q8zUEknfo09=CgeniWQxiTrmqDoxGkDi5oD0IbGDiiDBeD5xDTDWeDGDD3dSBw1U7tRDDdp9Yms4GWDmbEDY5DEhRvDiPhR4DWN4i1AngpuKxD0ra6gQQDN1Qo3fOvkfYDvaL_mASurNZD9DkTH40kmkZwmjSEG33mLdqjIa51GjPQ4x44N6Dx4rrkFGxKGb/iC8ubQiNAD4eAqGr5AiNj5PybXQuF3w4dG4dygb5lDr/ATTF04CG18Ri8bW/b5l0dX2eZxiK_xQoPDA/RgDsY47RZAGDD; ssxmod_itna2=1-iq_x9DcQ0QqCTxeKxmwd4QkP1D73DzxC5iOD_xQ5DODLxn4GQDUjYTng4QI3Yhw4PqD02DQ5dY48DQiDGfvpzdK47PhdAF2xD/Qicpif0SG7dhXqAUGLdeHESYdxwk2Kw347I6_5rOh5rcDUoF546aQ0lPFWn2zUBPQqCYmshiFTUa5sEYbKjyIdjDAf=88yofOvg4xhp3QL_0O=79ENWSF2PqPBUEdE6iOUixPvzw81Dq4G7Gx1BiNkSrCT1OuNqb5YPjIqfo1eiDbIxLf10C9ETiNVQiieo32qm/gLsz54D'

# 获取日K线数据并进行分析
def get_kline_data(code, start_date, count=400, period='day', XQ_COOKIE=''):
    url = f"https://stock.xueqiu.com/v5/stock/chart/kline.json?symbol={code.upper()}&begin={start_date}&period={period}&type=before&count=-{count}&indicator=kline"

    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Safari/537.36',
        'Cookie': XQ_COOKIE
    }
    

    response = requests.get(url, headers=headers)
```

请求例子：
https://stock.xueqiu.com/v5/stock/chart/kline.json?symbol=SZ300083&begin=1752721474588&period=day&type=before&count=-90&indicator=kline

参数解释：
begin：当前时间戳
symbol：大写的股票代码，如 SZ000333
period：周期，day，week
count：回溯天数，-90 为过去 90天，如果获取 800根k线就是 count=-800

响应结构为：
```
{"data":{"symbol":"SZ300083","column":["timestamp","volume","open","high","low","close","chg","percent","turnoverrate","amount","volume_post","amount_post"],"item":[[1752076800000,31485597,8.23,8.3,8.15,8.2,0.0,0.0,2.11,2.5850056E8,6000,49200.0],[1752163200000,43501870,8.19,8.33,8.14,8.28,0.08,0.98,2.91,3.58488868E8,3600,29808.0],[1752422400000,51128085,8.28,8.42,8.27,8.38,0.1,1.21,3.42,4.27619577E8,44400,372072.0],[1752508800000,59225437,8.39,8.53,8.25,8.37,-0.01,-0.12,3.97,4.95832186E8,1300,10881.0],[1752595200000,27728200,8.35,8.46,8.31,8.43,0.06,0.72,1.86,2.32831893E8,null,null]]},"error_code":0,"error_description":""}
```


